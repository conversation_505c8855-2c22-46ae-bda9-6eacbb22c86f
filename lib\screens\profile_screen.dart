import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../widgets/common_widgets.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Profile',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.mediumSpacing),
        child: Column(
          children: [
            _buildProfileHeader(),
            const SizedBox(height: AppConstants.largeSpacing),
            _buildStatsSection(),
            const SizedBox(height: AppConstants.largeSpacing),
            _buildSettingsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return CustomCard(
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
            child: Icon(
              Icons.person,
              size: 50,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          Text(
            'John Doe',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          Text(
            '<EMAIL>',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          CustomButton(
            text: 'Edit Profile',
            onPressed: () {
              // Navigate to edit profile
            },
            isOutlined: true,
            icon: Icons.edit,
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Stats',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: AppConstants.mediumSpacing),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                title: 'Total Habits',
                value: '12',
                icon: Icons.track_changes,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: AppConstants.smallSpacing),
            Expanded(
              child: _buildStatCard(
                title: 'Days Active',
                value: '45',
                icon: Icons.calendar_today,
                color: Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.smallSpacing),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                title: 'Current Streak',
                value: '7 days',
                icon: Icons.local_fire_department,
                color: Colors.orange,
              ),
            ),
            const SizedBox(width: AppConstants.smallSpacing),
            Expanded(
              child: _buildStatCard(
                title: 'Success Rate',
                value: '78%',
                icon: Icons.trending_up,
                color: Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return CustomCard(
      child: Column(
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: AppConstants.smallSpacing),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Settings',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: AppConstants.mediumSpacing),
        
        _buildSettingsGroup([
          _buildSettingsItem(
            icon: Icons.notifications,
            title: 'Notifications',
            subtitle: 'Manage your notification preferences',
            onTap: () {},
          ),
          _buildSettingsItem(
            icon: Icons.dark_mode,
            title: 'Dark Mode',
            subtitle: 'Switch between light and dark themes',
            onTap: () {},
            trailing: Switch(
              value: false,
              onChanged: (value) {},
            ),
          ),
          _buildSettingsItem(
            icon: Icons.language,
            title: 'Language',
            subtitle: 'English',
            onTap: () {},
          ),
        ]),
        
        const SizedBox(height: AppConstants.mediumSpacing),
        
        _buildSettingsGroup([
          _buildSettingsItem(
            icon: Icons.backup,
            title: 'Backup & Sync',
            subtitle: 'Backup your data to the cloud',
            onTap: () {},
          ),
          _buildSettingsItem(
            icon: Icons.file_download,
            title: 'Export Data',
            subtitle: 'Download your habit data',
            onTap: () {},
          ),
          _buildSettingsItem(
            icon: Icons.delete_forever,
            title: 'Clear All Data',
            subtitle: 'Remove all habits and progress',
            onTap: () {
              _showClearDataDialog();
            },
            textColor: Colors.red,
          ),
        ]),
        
        const SizedBox(height: AppConstants.mediumSpacing),
        
        _buildSettingsGroup([
          _buildSettingsItem(
            icon: Icons.help,
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onTap: () {},
          ),
          _buildSettingsItem(
            icon: Icons.info,
            title: 'About',
            subtitle: 'App version and information',
            onTap: () {},
          ),
          _buildSettingsItem(
            icon: Icons.logout,
            title: 'Sign Out',
            subtitle: 'Sign out of your account',
            onTap: () {
              _showSignOutDialog();
            },
            textColor: Colors.red,
          ),
        ]),
      ],
    );
  }

  Widget _buildSettingsGroup(List<Widget> items) {
    return CustomCard(
      padding: EdgeInsets.zero,
      child: Column(
        children: items,
      ),
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Widget? trailing,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? Theme.of(context).iconTheme.color,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: textColor,
        ),
      ),
      subtitle: Text(subtitle),
      trailing: trailing ?? const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'Are you sure you want to clear all your habit data? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Clear data logic
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Clear Data'),
          ),
        ],
      ),
    );
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Sign out logic
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
