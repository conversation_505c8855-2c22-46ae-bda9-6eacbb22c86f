import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../widgets/common_widgets.dart';

class HabitsScreen extends StatefulWidget {
  const HabitsScreen({super.key});

  @override
  State<HabitsScreen> createState() => _HabitsScreenState();
}

class _HabitsScreenState extends State<HabitsScreen> {
  String _selectedCategory = 'All';
  
  final List<String> _categories = ['All', ...AppConstants.habitCategories];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'My Habits',
      ),
      body: Column(
        children: [
          // Category filter
          _buildCategoryFilter(),
          
          // Habits list
          Expanded(
            child: _buildHabitsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: AppConstants.smallSpacing),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppConstants.mediumSpacing),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = category == _selectedCategory;
          
          return Padding(
            padding: const EdgeInsets.only(right: AppConstants.smallSpacing),
            child: FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              backgroundColor: Colors.grey[200],
              selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
              checkmarkColor: Theme.of(context).primaryColor,
            ),
          );
        },
      ),
    );
  }

  Widget _buildHabitsList() {
    // Sample habits data - this will be replaced with actual data
    final habits = _getSampleHabits();
    
    if (habits.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.track_changes,
        title: 'No Habits Yet',
        subtitle: 'Start building better habits by adding your first one!',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.smallSpacing),
      itemCount: habits.length,
      itemBuilder: (context, index) {
        final habit = habits[index];
        return _buildHabitCard(habit);
      },
    );
  }

  Widget _buildHabitCard(Map<String, dynamic> habit) {
    return CustomCard(
      onTap: () {
        // Navigate to habit details
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              HabitIconWidget(
                icon: habit['icon'],
                color: habit['color'],
                isCompleted: habit['isCompleted'],
              ),
              const SizedBox(width: AppConstants.mediumSpacing),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      habit['name'],
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      habit['category'],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.local_fire_department,
                        size: 16,
                        color: Colors.orange,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${habit['streak']} days',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    habit['frequency'],
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.mediumSpacing),
          
          // Progress bar
          CustomProgressIndicator(
            progress: habit['progress'],
            label: 'This week: ${(habit['progress'] * 100).toInt()}%',
          ),
          
          const SizedBox(height: AppConstants.smallSpacing),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton.icon(
                onPressed: () {
                  // Edit habit
                },
                icon: const Icon(Icons.edit, size: 16),
                label: const Text('Edit'),
              ),
              const SizedBox(width: AppConstants.smallSpacing),
              ElevatedButton.icon(
                onPressed: habit['isCompleted'] ? null : () {
                  // Mark as completed
                },
                icon: Icon(
                  habit['isCompleted'] ? Icons.check_circle : Icons.check,
                  size: 16,
                ),
                label: Text(habit['isCompleted'] ? 'Completed' : 'Mark Done'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: habit['isCompleted'] ? Colors.green : null,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getSampleHabits() {
    return [
      {
        'name': 'Morning Exercise',
        'category': 'Health & Fitness',
        'icon': Icons.fitness_center,
        'color': Colors.blue,
        'streak': 7,
        'frequency': 'Daily',
        'progress': 0.8,
        'isCompleted': true,
      },
      {
        'name': 'Drink 8 Glasses of Water',
        'category': 'Health & Fitness',
        'icon': Icons.local_drink,
        'color': Colors.cyan,
        'streak': 5,
        'frequency': 'Daily',
        'progress': 0.6,
        'isCompleted': false,
      },
      {
        'name': 'Read for 30 Minutes',
        'category': 'Learning',
        'icon': Icons.book,
        'color': Colors.purple,
        'streak': 12,
        'frequency': 'Daily',
        'progress': 0.9,
        'isCompleted': false,
      },
      {
        'name': 'Meditate',
        'category': 'Mindfulness',
        'icon': Icons.self_improvement,
        'color': Colors.green,
        'streak': 3,
        'frequency': 'Daily',
        'progress': 0.4,
        'isCompleted': false,
      },
    ];
  }
}
