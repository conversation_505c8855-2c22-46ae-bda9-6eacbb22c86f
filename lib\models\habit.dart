import 'package:hive/hive.dart';

part 'habit.g.dart';

@HiveType(typeId: 0)
class Habit extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String description;

  @HiveField(3)
  String category;

  @HiveField(4)
  int color;

  @HiveField(5)
  String icon;

  @HiveField(6)
  HabitFrequency frequency;

  @HiveField(7)
  List<String> selectedDays; // For weekly habits

  @HiveField(8)
  DateTime createdAt;

  @HiveField(9)
  DateTime? targetDate;

  @HiveField(10)
  bool isActive;

  @HiveField(11)
  List<HabitProgress> progress;

  @HiveField(12)
  int currentStreak;

  @HiveField(13)
  int longestStreak;

  @HiveField(14)
  String? reminderTime;

  @HiveField(15)
  bool reminderEnabled;

  Habit({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.color,
    required this.icon,
    required this.frequency,
    required this.selectedDays,
    required this.createdAt,
    this.targetDate,
    this.isActive = true,
    required this.progress,
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.reminderTime,
    this.reminderEnabled = false,
  });

  // Calculate completion percentage for today
  double get todayCompletionPercentage {
    final today = DateTime.now();
    final todayProgress = progress.where((p) => 
      p.date.year == today.year && 
      p.date.month == today.month && 
      p.date.day == today.day
    ).toList();
    
    if (todayProgress.isEmpty) return 0.0;
    return todayProgress.first.isCompleted ? 1.0 : 0.0;
  }

  // Calculate overall completion percentage
  double get overallCompletionPercentage {
    if (progress.isEmpty) return 0.0;
    final completedDays = progress.where((p) => p.isCompleted).length;
    return completedDays / progress.length;
  }

  // Check if habit should be done today
  bool get shouldDoToday {
    final today = DateTime.now();
    final dayName = _getDayName(today.weekday);
    
    switch (frequency) {
      case HabitFrequency.daily:
        return true;
      case HabitFrequency.weekly:
        return selectedDays.contains(dayName);
      case HabitFrequency.monthly:
        return today.day == 1; // First day of month
      default:
        return false;
    }
  }

  String _getDayName(int weekday) {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[weekday - 1];
  }

  // Update streak based on progress
  void updateStreak() {
    if (progress.isEmpty) {
      currentStreak = 0;
      return;
    }

    // Sort progress by date
    progress.sort((a, b) => b.date.compareTo(a.date));
    
    int streak = 0;
    DateTime currentDate = DateTime.now();
    
    for (var p in progress) {
      if (p.isCompleted && _isSameDay(p.date, currentDate)) {
        streak++;
        currentDate = currentDate.subtract(const Duration(days: 1));
      } else {
        break;
      }
    }
    
    currentStreak = streak;
    if (currentStreak > longestStreak) {
      longestStreak = currentStreak;
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && 
           date1.month == date2.month && 
           date1.day == date2.day;
  }
}

@HiveType(typeId: 1)
enum HabitFrequency {
  @HiveField(0)
  daily,
  @HiveField(1)
  weekly,
  @HiveField(2)
  monthly,
}

@HiveType(typeId: 2)
class HabitProgress extends HiveObject {
  @HiveField(0)
  String habitId;

  @HiveField(1)
  DateTime date;

  @HiveField(2)
  bool isCompleted;

  @HiveField(3)
  String? notes;

  @HiveField(4)
  DateTime? completedAt;

  HabitProgress({
    required this.habitId,
    required this.date,
    required this.isCompleted,
    this.notes,
    this.completedAt,
  });
}
