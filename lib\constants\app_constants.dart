import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'Habit Tracker';
  static const String appVersion = '1.0.0';
  
  // Database
  static const String habitBoxName = 'habits';
  static const String userBoxName = 'users';
  static const String analyticsBoxName = 'analytics';
  static const String settingsBoxName = 'settings';
  
  // SharedPreferences Keys
  static const String keyFirstLaunch = 'first_launch';
  static const String keyOnboardingCompleted = 'onboarding_completed';
  static const String keyCurrentUserId = 'current_user_id';
  static const String keyThemeMode = 'theme_mode';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  
  // Habit Categories
  static const List<String> habitCategories = [
    'Health & Fitness',
    'Productivity',
    'Learning',
    'Mindfulness',
    'Social',
    'Creativity',
    'Finance',
    'Personal Care',
    'Environment',
    'Other',
  ];
  
  // Habit Category Icons
  static const Map<String, IconData> categoryIcons = {
    'Health & Fitness': Icons.fitness_center,
    'Productivity': Icons.work,
    'Learning': Icons.school,
    'Mindfulness': Icons.self_improvement,
    'Social': Icons.people,
    'Creativity': Icons.palette,
    'Finance': Icons.attach_money,
    'Personal Care': Icons.spa,
    'Environment': Icons.eco,
    'Other': Icons.category,
  };
  
  // Habit Category Colors
  static const Map<String, Color> categoryColors = {
    'Health & Fitness': Color(0xFF4CAF50),
    'Productivity': Color(0xFF2196F3),
    'Learning': Color(0xFF9C27B0),
    'Mindfulness': Color(0xFF00BCD4),
    'Social': Color(0xFFFF9800),
    'Creativity': Color(0xFFE91E63),
    'Finance': Color(0xFF4CAF50),
    'Personal Care': Color(0xFFFF5722),
    'Environment': Color(0xFF8BC34A),
    'Other': Color(0xFF607D8B),
  };
  
  // Habit Icons
  static const List<IconData> habitIcons = [
    Icons.fitness_center,
    Icons.local_drink,
    Icons.book,
    Icons.music_note,
    Icons.directions_run,
    Icons.restaurant,
    Icons.bedtime,
    Icons.work,
    Icons.school,
    Icons.self_improvement,
    Icons.people,
    Icons.palette,
    Icons.attach_money,
    Icons.spa,
    Icons.eco,
    Icons.phone,
    Icons.computer,
    Icons.car_rental,
    Icons.home,
    Icons.shopping_cart,
    Icons.camera,
    Icons.games,
    Icons.movie,
    Icons.travel_explore,
    Icons.pets,
  ];
  
  // Habit Colors
  static const List<Color> habitColors = [
    Color(0xFFE57373), // Red
    Color(0xFFBA68C8), // Purple
    Color(0xFF64B5F6), // Blue
    Color(0xFF4FC3F7), // Light Blue
    Color(0xFF4DD0E1), // Cyan
    Color(0xFF4DB6AC), // Teal
    Color(0xFF81C784), // Green
    Color(0xFFAED581), // Light Green
    Color(0xFFDCE775), // Lime
    Color(0xFFFFD54F), // Amber
    Color(0xFFFFB74D), // Orange
    Color(0xFFFF8A65), // Deep Orange
    Color(0xFFA1887F), // Brown
    Color(0xFF90A4AE), // Blue Grey
  ];
  
  // Days of the week
  static const List<String> daysOfWeek = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];
  
  static const List<String> daysOfWeekShort = [
    'Mon',
    'Tue',
    'Wed',
    'Thu',
    'Fri',
    'Sat',
    'Sun',
  ];
  
  // Frequency options
  static const List<String> frequencyOptions = [
    'Daily',
    'Weekly',
    'Monthly',
  ];
  
  // Reminder times
  static const List<String> reminderTimes = [
    '06:00',
    '07:00',
    '08:00',
    '09:00',
    '10:00',
    '11:00',
    '12:00',
    '13:00',
    '14:00',
    '15:00',
    '16:00',
    '17:00',
    '18:00',
    '19:00',
    '20:00',
    '21:00',
    '22:00',
  ];
  
  // Animation durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 400);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);
  
  // Spacing
  static const double smallSpacing = 8.0;
  static const double mediumSpacing = 16.0;
  static const double largeSpacing = 24.0;
  static const double extraLargeSpacing = 32.0;
  
  // Border radius
  static const double smallBorderRadius = 8.0;
  static const double mediumBorderRadius = 12.0;
  static const double largeBorderRadius = 16.0;
  static const double extraLargeBorderRadius = 24.0;
  
  // Elevation
  static const double lowElevation = 2.0;
  static const double mediumElevation = 4.0;
  static const double highElevation = 8.0;
  
  // Notification IDs
  static const int habitReminderNotificationId = 1000;
  static const int motivationalNotificationId = 2000;
  
  // Onboarding
  static const List<String> onboardingTitles = [
    'Welcome to Habit Tracker',
    'Track Your Progress',
    'Build Streaks',
    'Get Insights',
  ];
  
  static const List<String> onboardingDescriptions = [
    'Start building better habits and transform your life one day at a time.',
    'Monitor your daily progress and see how far you\'ve come.',
    'Build momentum with streak tracking and stay motivated.',
    'Get detailed analytics and insights about your habit patterns.',
  ];
  
  // Motivational quotes
  static const List<String> motivationalQuotes = [
    'The secret of getting ahead is getting started.',
    'Success is the sum of small efforts repeated day in and day out.',
    'We are what we repeatedly do. Excellence, then, is not an act, but a habit.',
    'The groundwork for all happiness is good health.',
    'Take care of your body. It\'s the only place you have to live.',
    'A goal without a plan is just a wish.',
    'Progress, not perfection.',
    'Small steps every day lead to big changes.',
  ];
}
