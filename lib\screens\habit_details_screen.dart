import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../widgets/common_widgets.dart';
import '../models/habit.dart';
import 'add_habit_screen.dart';

class HabitDetailsScreen extends StatefulWidget {
  final Habit habit;

  const HabitDetailsScreen({super.key, required this.habit});

  @override
  State<HabitDetailsScreen> createState() => _HabitDetailsScreenState();
}

class _HabitDetailsScreenState extends State<HabitDetailsScreen> {
  late Habit _habit;

  @override
  void initState() {
    super.initState();
    _habit = widget.habit;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _habit.name,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editHabit,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'archive',
                child: Row(
                  children: [
                    Icon(Icons.archive),
                    SizedBox(width: 8),
                    Text('Archive'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.mediumSpacing),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHabitHeader(),
            const SizedBox(height: AppConstants.largeSpacing),
            _buildProgressSection(),
            const SizedBox(height: AppConstants.largeSpacing),
            _buildStreakSection(),
            const SizedBox(height: AppConstants.largeSpacing),
            _buildCalendarView(),
            const SizedBox(height: AppConstants.largeSpacing),
            _buildStatisticsSection(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _markAsCompleted,
        icon: Icon(_habit.isCompletedToday ? Icons.check_circle : Icons.check),
        label: Text(_habit.isCompletedToday ? 'Completed' : 'Mark Done'),
        backgroundColor: _habit.isCompletedToday ? Colors.green : null,
      ),
    );
  }

  Widget _buildHabitHeader() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              HabitIconWidget(
                icon: IconData(int.parse(_habit.icon)),
                color: Color(_habit.color),
                isCompleted: _habit.isCompletedToday,
                size: 48,
              ),
              const SizedBox(width: AppConstants.mediumSpacing),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _habit.name,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _habit.category,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getFrequencyText(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (_habit.description.isNotEmpty) ...[
            const SizedBox(height: AppConstants.mediumSpacing),
            Text(
              _habit.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Today\'s Progress',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          CustomProgressIndicator(
            progress: _habit.todayCompletionPercentage,
            label: _habit.isCompletedToday 
                ? 'Completed for today!' 
                : 'Not completed yet',
            height: 12,
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          Text(
            '${(_habit.todayCompletionPercentage * 100).toInt()}% Complete',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: _habit.isCompletedToday ? Colors.green : Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStreakSection() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Current Streak',
            value: '${_habit.currentStreak} days',
            icon: Icons.local_fire_department,
            color: Colors.orange,
          ),
        ),
        const SizedBox(width: AppConstants.smallSpacing),
        Expanded(
          child: _buildStatCard(
            title: 'Best Streak',
            value: '${_habit.bestStreak} days',
            icon: Icons.emoji_events,
            color: Colors.amber,
          ),
        ),
        const SizedBox(width: AppConstants.smallSpacing),
        Expanded(
          child: _buildStatCard(
            title: 'Total Days',
            value: '${_habit.progress.length}',
            icon: Icons.calendar_today,
            color: Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return CustomCard(
      child: Column(
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: AppConstants.smallSpacing),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarView() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Calendar View',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          
          // Calendar grid for the current month
          _buildCalendarGrid(),
        ],
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final now = DateTime.now();
    final firstDayOfMonth = DateTime(now.year, now.month, 1);
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);
    final daysInMonth = lastDayOfMonth.day;
    final startWeekday = firstDayOfMonth.weekday % 7;

    return Column(
      children: [
        // Month header
        Text(
          '${_getMonthName(now.month)} ${now.year}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: AppConstants.smallSpacing),
        
        // Weekday headers
        Row(
          children: ['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day) {
            return Expanded(
              child: Center(
                child: Text(
                  day,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: AppConstants.smallSpacing),
        
        // Calendar days
        ...List.generate((daysInMonth + startWeekday + 6) ~/ 7, (weekIndex) {
          return Row(
            children: List.generate(7, (dayIndex) {
              final dayNumber = weekIndex * 7 + dayIndex - startWeekday + 1;
              if (dayNumber < 1 || dayNumber > daysInMonth) {
                return const Expanded(child: SizedBox(height: 40));
              }
              
              final date = DateTime(now.year, now.month, dayNumber);
              final isCompleted = _habit.progress.any((p) => 
                p.date.year == date.year &&
                p.date.month == date.month &&
                p.date.day == date.day &&
                p.isCompleted
              );
              final isToday = date.day == now.day;
              
              return Expanded(
                child: Container(
                  height: 40,
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: isCompleted 
                        ? Colors.green 
                        : isToday 
                            ? Theme.of(context).primaryColor.withOpacity(0.2)
                            : null,
                    borderRadius: BorderRadius.circular(8),
                    border: isToday 
                        ? Border.all(color: Theme.of(context).primaryColor)
                        : null,
                  ),
                  child: Center(
                    child: Text(
                      dayNumber.toString(),
                      style: TextStyle(
                        color: isCompleted 
                            ? Colors.white 
                            : isToday 
                                ? Theme.of(context).primaryColor
                                : null,
                        fontWeight: isToday ? FontWeight.bold : null,
                      ),
                    ),
                  ),
                ),
              );
            }),
          );
        }),
      ],
    );
  }

  Widget _buildStatisticsSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Statistics',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          
          _buildStatRow('Completion Rate', '${(_habit.completionRate * 100).toInt()}%'),
          _buildStatRow('Days Active', '${DateTime.now().difference(_habit.createdAt).inDays + 1}'),
          _buildStatRow('Times Completed', '${_habit.progress.where((p) => p.isCompleted).length}'),
          _buildStatRow('Created', _formatDate(_habit.createdAt)),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String _getFrequencyText() {
    switch (_habit.frequency) {
      case HabitFrequency.daily:
        return 'Daily';
      case HabitFrequency.weekly:
        return 'Weekly (${_habit.selectedDays.join(', ')})';
      case HabitFrequency.monthly:
        return 'Monthly';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _editHabit() async {
    final result = await Navigator.of(context).push<Habit>(
      MaterialPageRoute(
        builder: (context) => AddHabitScreen(habitToEdit: _habit),
      ),
    );
    
    if (result != null) {
      setState(() {
        _habit = result;
      });
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'archive':
        _showArchiveDialog();
        break;
      case 'delete':
        _showDeleteDialog();
        break;
    }
  }

  void _showArchiveDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Archive Habit'),
        content: Text('Are you sure you want to archive "${_habit.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Archive habit
              Navigator.of(context).pop();
            },
            child: const Text('Archive'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Habit'),
        content: Text('Are you sure you want to delete "${_habit.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Delete habit
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _markAsCompleted() {
    setState(() {
      // TODO: Update habit completion status
    });
  }
}
