// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AnalyticsAdapter extends TypeAdapter<Analytics> {
  @override
  final int typeId = 6;

  @override
  Analytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Analytics(
      id: fields[0] as String,
      date: fields[1] as DateTime,
      totalHabitsCompleted: fields[2] as int,
      totalHabitsScheduled: fields[3] as int,
      completionRate: fields[4] as double,
      categoryCompletions: (fields[5] as Map).cast<String, int>(),
      streakDays: fields[6] as int,
      completedHabitIds: (fields[7] as List).cast<String>(),
      createdAt: fields[8] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, Analytics obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.date)
      ..writeByte(2)
      ..write(obj.totalHabitsCompleted)
      ..writeByte(3)
      ..write(obj.totalHabitsScheduled)
      ..writeByte(4)
      ..write(obj.completionRate)
      ..writeByte(5)
      ..write(obj.categoryCompletions)
      ..writeByte(6)
      ..write(obj.streakDays)
      ..writeByte(7)
      ..write(obj.completedHabitIds)
      ..writeByte(8)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class WeeklyAnalyticsAdapter extends TypeAdapter<WeeklyAnalytics> {
  @override
  final int typeId = 7;

  @override
  WeeklyAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return WeeklyAnalytics(
      id: fields[0] as String,
      weekStartDate: fields[1] as DateTime,
      weekEndDate: fields[2] as DateTime,
      dailyAnalytics: (fields[3] as List).cast<Analytics>(),
      averageCompletionRate: fields[4] as double,
      totalHabitsCompleted: fields[5] as int,
      totalHabitsScheduled: fields[6] as int,
      topCategories: (fields[7] as Map).cast<String, int>(),
      bestStreakDay: fields[8] as int,
      createdAt: fields[9] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, WeeklyAnalytics obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.weekStartDate)
      ..writeByte(2)
      ..write(obj.weekEndDate)
      ..writeByte(3)
      ..write(obj.dailyAnalytics)
      ..writeByte(4)
      ..write(obj.averageCompletionRate)
      ..writeByte(5)
      ..write(obj.totalHabitsCompleted)
      ..writeByte(6)
      ..write(obj.totalHabitsScheduled)
      ..writeByte(7)
      ..write(obj.topCategories)
      ..writeByte(8)
      ..write(obj.bestStreakDay)
      ..writeByte(9)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WeeklyAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MonthlyAnalyticsAdapter extends TypeAdapter<MonthlyAnalytics> {
  @override
  final int typeId = 8;

  @override
  MonthlyAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MonthlyAnalytics(
      id: fields[0] as String,
      month: fields[1] as int,
      year: fields[2] as int,
      weeklyAnalytics: (fields[3] as List).cast<WeeklyAnalytics>(),
      averageCompletionRate: fields[4] as double,
      totalHabitsCompleted: fields[5] as int,
      totalHabitsScheduled: fields[6] as int,
      categoryBreakdown: (fields[7] as Map).cast<String, int>(),
      longestStreak: fields[8] as int,
      dailyCompletions: (fields[9] as List).cast<int>(),
      createdAt: fields[10] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, MonthlyAnalytics obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.month)
      ..writeByte(2)
      ..write(obj.year)
      ..writeByte(3)
      ..write(obj.weeklyAnalytics)
      ..writeByte(4)
      ..write(obj.averageCompletionRate)
      ..writeByte(5)
      ..write(obj.totalHabitsCompleted)
      ..writeByte(6)
      ..write(obj.totalHabitsScheduled)
      ..writeByte(7)
      ..write(obj.categoryBreakdown)
      ..writeByte(8)
      ..write(obj.longestStreak)
      ..writeByte(9)
      ..write(obj.dailyCompletions)
      ..writeByte(10)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MonthlyAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DailyAnalyticsAdapter extends TypeAdapter<DailyAnalytics> {
  @override
  final int typeId = 9;

  @override
  DailyAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DailyAnalytics(
      date: fields[0] as DateTime,
      totalHabits: fields[1] as int,
      completedHabits: fields[2] as int,
      completionRate: fields[3] as double,
      categoryBreakdown: (fields[4] as Map).cast<String, int>(),
    );
  }

  @override
  void write(BinaryWriter writer, DailyAnalytics obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.date)
      ..writeByte(1)
      ..write(obj.totalHabits)
      ..writeByte(2)
      ..write(obj.completedHabits)
      ..writeByte(3)
      ..write(obj.completionRate)
      ..writeByte(4)
      ..write(obj.categoryBreakdown);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DailyAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
