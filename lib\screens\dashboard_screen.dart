import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../widgets/common_widgets.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Dashboard',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.mediumSpacing),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome message
            _buildWelcomeCard(),
            
            const SizedBox(height: AppConstants.mediumSpacing),
            
            // Today's progress
            _buildTodayProgressCard(),
            
            const SizedBox(height: AppConstants.mediumSpacing),
            
            // Quick stats
            _buildQuickStatsRow(),
            
            const SizedBox(height: AppConstants.mediumSpacing),
            
            // Today's habits
            _buildTodayHabitsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.wb_sunny,
                color: Colors.orange,
                size: 28,
              ),
              const SizedBox(width: AppConstants.smallSpacing),
              Text(
                'Good Morning!',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          Text(
            'Ready to build some great habits today?',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayProgressCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Today\'s Progress',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          const CustomProgressIndicator(
            progress: 0.6,
            label: '3 of 5 habits completed',
            height: 12,
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          Text(
            '60% Complete',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStatsRow() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            icon: Icons.local_fire_department,
            title: 'Current Streak',
            value: '7 days',
            color: Colors.orange,
          ),
        ),
        const SizedBox(width: AppConstants.smallSpacing),
        Expanded(
          child: _buildStatCard(
            icon: Icons.trending_up,
            title: 'This Week',
            value: '85%',
            color: Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return CustomCard(
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 32,
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTodayHabitsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Today\'s Habits',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            TextButton(
              onPressed: () {
                // Navigate to habits screen
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.smallSpacing),
        
        // Sample habit items
        _buildHabitItem(
          icon: Icons.fitness_center,
          title: 'Morning Exercise',
          isCompleted: true,
          color: Colors.blue,
        ),
        _buildHabitItem(
          icon: Icons.local_drink,
          title: 'Drink Water',
          isCompleted: true,
          color: Colors.cyan,
        ),
        _buildHabitItem(
          icon: Icons.book,
          title: 'Read for 30 minutes',
          isCompleted: false,
          color: Colors.purple,
        ),
      ],
    );
  }

  Widget _buildHabitItem({
    required IconData icon,
    required String title,
    required bool isCompleted,
    required Color color,
  }) {
    return CustomCard(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          HabitIconWidget(
            icon: icon,
            color: color,
            isCompleted: isCompleted,
          ),
          const SizedBox(width: AppConstants.mediumSpacing),
          Expanded(
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                decoration: isCompleted ? TextDecoration.lineThrough : null,
                color: isCompleted ? Colors.grey : null,
              ),
            ),
          ),
          if (isCompleted)
            const Icon(
              Icons.check_circle,
              color: Colors.green,
            )
          else
            IconButton(
              onPressed: () {
                // Mark as completed
              },
              icon: const Icon(Icons.radio_button_unchecked),
            ),
        ],
      ),
    );
  }
}
