import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';
import '../models/habit.dart';
import '../constants/app_constants.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
  }

  Future<bool> requestPermissions() async {
    await initialize();
    
    if (defaultTargetPlatform == TargetPlatform.android) {
      final androidPlugin = _notifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
      return await androidPlugin?.requestNotificationsPermission() ?? false;
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      final iosPlugin = _notifications.resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>();
      return await iosPlugin?.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      ) ?? false;
    }
    
    return true;
  }

  Future<void> scheduleHabitReminder(Habit habit) async {
    if (!habit.reminderEnabled || habit.reminderTime == null) return;
    
    await initialize();
    
    final timeParts = habit.reminderTime!.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);
    
    const androidDetails = AndroidNotificationDetails(
      'habit_reminders',
      'Habit Reminders',
      channelDescription: 'Notifications to remind you about your habits',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // Schedule based on frequency
    switch (habit.frequency) {
      case HabitFrequency.daily:
        await _notifications.zonedSchedule(
          habit.id.hashCode,
          'Time for ${habit.name}!',
          'Don\'t forget to complete your habit today.',
          _nextInstanceOfTime(hour, minute),
          notificationDetails,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
          matchDateTimeComponents: DateTimeComponents.time,
        );
        break;
        
      case HabitFrequency.weekly:
        for (final day in habit.selectedDays) {
          final weekday = _getWeekdayNumber(day);
          await _notifications.zonedSchedule(
            '${habit.id}_$day'.hashCode,
            'Time for ${habit.name}!',
            'Don\'t forget to complete your habit today.',
            _nextInstanceOfWeekday(weekday, hour, minute),
            notificationDetails,
            androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
            uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
            matchDateTimeComponents: DateTimeComponents.dayOfWeekAndTime,
          );
        }
        break;
        
      case HabitFrequency.monthly:
        await _notifications.zonedSchedule(
          habit.id.hashCode,
          'Time for ${habit.name}!',
          'Don\'t forget to complete your habit this month.',
          _nextInstanceOfMonth(hour, minute),
          notificationDetails,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
          matchDateTimeComponents: DateTimeComponents.dayOfMonthAndTime,
        );
        break;
    }
  }

  Future<void> cancelHabitReminder(String habitId) async {
    await initialize();
    
    // Cancel main notification
    await _notifications.cancel(habitId.hashCode);
    
    // Cancel weekly notifications for all days
    for (final day in AppConstants.daysOfWeek) {
      await _notifications.cancel('${habitId}_$day'.hashCode);
    }
  }

  Future<void> scheduleMotivationalNotification() async {
    await initialize();
    
    const androidDetails = AndroidNotificationDetails(
      'motivational',
      'Motivational Messages',
      channelDescription: 'Daily motivational messages to keep you going',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
      icon: '@mipmap/ic_launcher',
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    final quote = AppConstants.motivationalQuotes[
      DateTime.now().day % AppConstants.motivationalQuotes.length
    ];

    await _notifications.zonedSchedule(
      'motivational'.hashCode,
      'Daily Motivation',
      quote,
      _nextInstanceOfTime(20, 0), // 8 PM
      notificationDetails,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  Future<void> showCompletionCelebration(String habitName) async {
    await initialize();
    
    const androidDetails = AndroidNotificationDetails(
      'celebrations',
      'Habit Celebrations',
      channelDescription: 'Celebrate your habit completions',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      playSound: true,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      'celebration'.hashCode,
      'Great job! 🎉',
      'You completed "$habitName" today. Keep up the great work!',
      notificationDetails,
    );
  }

  Future<void> showStreakMilestone(int streakDays) async {
    await initialize();
    
    const androidDetails = AndroidNotificationDetails(
      'milestones',
      'Streak Milestones',
      channelDescription: 'Celebrate your streak milestones',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      playSound: true,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    String message;
    if (streakDays == 7) {
      message = 'Amazing! You\'ve maintained your habits for a whole week! 🔥';
    } else if (streakDays == 30) {
      message = 'Incredible! You\'ve hit a 30-day streak! You\'re building lasting habits! 🏆';
    } else if (streakDays == 100) {
      message = 'Phenomenal! 100 days of consistency! You\'re a habit master! 👑';
    } else if (streakDays % 10 == 0) {
      message = 'Fantastic! $streakDays days in a row! Keep the momentum going! 🚀';
    } else {
      return; // Don't show notification for other numbers
    }

    await _notifications.show(
      'milestone'.hashCode,
      'Streak Milestone! 🎯',
      message,
      notificationDetails,
    );
  }

  Future<void> cancelAllNotifications() async {
    await initialize();
    await _notifications.cancelAll();
  }

  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    await initialize();
    return await _notifications.pendingNotificationRequests();
  }

  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    // This could navigate to specific screens or perform actions
    debugPrint('Notification tapped: ${response.payload}');
  }

  TZDateTime _nextInstanceOfTime(int hour, int minute) {
    final now = TZDateTime.now(local);
    var scheduledDate = TZDateTime(local, now.year, now.month, now.day, hour, minute);
    
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }
    
    return scheduledDate;
  }

  TZDateTime _nextInstanceOfWeekday(int weekday, int hour, int minute) {
    final now = TZDateTime.now(local);
    var scheduledDate = TZDateTime(local, now.year, now.month, now.day, hour, minute);
    
    while (scheduledDate.weekday != weekday || scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }
    
    return scheduledDate;
  }

  TZDateTime _nextInstanceOfMonth(int hour, int minute) {
    final now = TZDateTime.now(local);
    var scheduledDate = TZDateTime(local, now.year, now.month, 1, hour, minute);
    
    if (scheduledDate.isBefore(now)) {
      scheduledDate = TZDateTime(local, now.year, now.month + 1, 1, hour, minute);
    }
    
    return scheduledDate;
  }

  int _getWeekdayNumber(String day) {
    switch (day.toLowerCase()) {
      case 'monday':
        return DateTime.monday;
      case 'tuesday':
        return DateTime.tuesday;
      case 'wednesday':
        return DateTime.wednesday;
      case 'thursday':
        return DateTime.thursday;
      case 'friday':
        return DateTime.friday;
      case 'saturday':
        return DateTime.saturday;
      case 'sunday':
        return DateTime.sunday;
      default:
        return DateTime.monday;
    }
  }
}

// Import for timezone
import 'package:timezone/timezone.dart' as tz;
typedef TZDateTime = tz.TZDateTime;
final tz.Location local = tz.local;
