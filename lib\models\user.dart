import 'package:hive/hive.dart';

part 'user.g.dart';

@HiveType(typeId: 3)
class User extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String email;

  @HiveField(3)
  String? profileImagePath;

  @HiveField(4)
  DateTime createdAt;

  @HiveField(5)
  DateTime lastLoginAt;

  @HiveField(6)
  UserPreferences preferences;

  @HiveField(7)
  UserStats stats;

  @HiveField(8)
  bool hasCompletedOnboarding;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.profileImagePath,
    required this.createdAt,
    required this.lastLoginAt,
    required this.preferences,
    required this.stats,
    this.hasCompletedOnboarding = false,
  });

  // Factory constructor for creating a new user
  factory User.create({
    required String name,
    required String email,
  }) {
    return User(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      email: email,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      preferences: UserPreferences.defaultPreferences(),
      stats: UserStats.initial(),
    );
  }
}

@HiveType(typeId: 4)
class UserPreferences extends HiveObject {
  @HiveField(0)
  bool notificationsEnabled;

  @HiveField(1)
  String defaultReminderTime;

  @HiveField(2)
  bool darkModeEnabled;

  @HiveField(3)
  String language;

  @HiveField(4)
  bool soundEnabled;

  @HiveField(5)
  bool vibrationEnabled;

  @HiveField(6)
  int weekStartDay; // 1 = Monday, 7 = Sunday

  @HiveField(7)
  String dateFormat;

  @HiveField(8)
  String timeFormat;

  UserPreferences({
    required this.notificationsEnabled,
    required this.defaultReminderTime,
    required this.darkModeEnabled,
    required this.language,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.weekStartDay,
    required this.dateFormat,
    required this.timeFormat,
  });

  factory UserPreferences.defaultPreferences() {
    return UserPreferences(
      notificationsEnabled: true,
      defaultReminderTime: '09:00',
      darkModeEnabled: false,
      language: 'en',
      soundEnabled: true,
      vibrationEnabled: true,
      weekStartDay: 1, // Monday
      dateFormat: 'dd/MM/yyyy',
      timeFormat: '24h',
    );
  }
}

@HiveType(typeId: 5)
class UserStats extends HiveObject {
  @HiveField(0)
  int totalHabits;

  @HiveField(1)
  int activeHabits;

  @HiveField(2)
  int completedHabits;

  @HiveField(3)
  int totalDaysTracked;

  @HiveField(4)
  int currentStreakDays;

  @HiveField(5)
  int longestStreakDays;

  @HiveField(6)
  double averageCompletionRate;

  @HiveField(7)
  DateTime lastUpdated;

  @HiveField(8)
  Map<String, int> categoryStats; // Category name -> completion count

  @HiveField(9)
  List<int> weeklyCompletions; // Last 7 days completion counts

  @HiveField(10)
  List<int> monthlyCompletions; // Last 30 days completion counts

  UserStats({
    required this.totalHabits,
    required this.activeHabits,
    required this.completedHabits,
    required this.totalDaysTracked,
    required this.currentStreakDays,
    required this.longestStreakDays,
    required this.averageCompletionRate,
    required this.lastUpdated,
    required this.categoryStats,
    required this.weeklyCompletions,
    required this.monthlyCompletions,
  });

  factory UserStats.initial() {
    return UserStats(
      totalHabits: 0,
      activeHabits: 0,
      completedHabits: 0,
      totalDaysTracked: 0,
      currentStreakDays: 0,
      longestStreakDays: 0,
      averageCompletionRate: 0.0,
      lastUpdated: DateTime.now(),
      categoryStats: {},
      weeklyCompletions: List.filled(7, 0),
      monthlyCompletions: List.filled(30, 0),
    );
  }

  // Update stats based on current habits and progress
  void updateStats(List<dynamic> habits) {
    totalHabits = habits.length;
    activeHabits = habits.where((h) => h.isActive).length;
    
    // Calculate other stats...
    lastUpdated = DateTime.now();
  }
}
