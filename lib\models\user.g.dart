// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserAdapter extends TypeAdapter<User> {
  @override
  final int typeId = 3;

  @override
  User read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return User(
      id: fields[0] as String,
      name: fields[1] as String,
      email: fields[2] as String,
      profileImagePath: fields[3] as String?,
      createdAt: fields[4] as DateTime,
      lastLoginAt: fields[5] as DateTime,
      preferences: fields[6] as UserPreferences,
      stats: fields[7] as UserStats,
      hasCompletedOnboarding: fields[8] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, User obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.email)
      ..writeByte(3)
      ..write(obj.profileImagePath)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.lastLoginAt)
      ..writeByte(6)
      ..write(obj.preferences)
      ..writeByte(7)
      ..write(obj.stats)
      ..writeByte(8)
      ..write(obj.hasCompletedOnboarding);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserPreferencesAdapter extends TypeAdapter<UserPreferences> {
  @override
  final int typeId = 4;

  @override
  UserPreferences read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserPreferences(
      notificationsEnabled: fields[0] as bool,
      defaultReminderTime: fields[1] as String,
      darkModeEnabled: fields[2] as bool,
      language: fields[3] as String,
      soundEnabled: fields[4] as bool,
      vibrationEnabled: fields[5] as bool,
      weekStartDay: fields[6] as int,
      dateFormat: fields[7] as String,
      timeFormat: fields[8] as String,
    );
  }

  @override
  void write(BinaryWriter writer, UserPreferences obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.notificationsEnabled)
      ..writeByte(1)
      ..write(obj.defaultReminderTime)
      ..writeByte(2)
      ..write(obj.darkModeEnabled)
      ..writeByte(3)
      ..write(obj.language)
      ..writeByte(4)
      ..write(obj.soundEnabled)
      ..writeByte(5)
      ..write(obj.vibrationEnabled)
      ..writeByte(6)
      ..write(obj.weekStartDay)
      ..writeByte(7)
      ..write(obj.dateFormat)
      ..writeByte(8)
      ..write(obj.timeFormat);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserPreferencesAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserStatsAdapter extends TypeAdapter<UserStats> {
  @override
  final int typeId = 5;

  @override
  UserStats read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserStats(
      totalHabits: fields[0] as int,
      activeHabits: fields[1] as int,
      completedHabits: fields[2] as int,
      totalDaysTracked: fields[3] as int,
      currentStreakDays: fields[4] as int,
      longestStreakDays: fields[5] as int,
      averageCompletionRate: fields[6] as double,
      lastUpdated: fields[7] as DateTime,
      categoryStats: (fields[8] as Map).cast<String, int>(),
      weeklyCompletions: (fields[9] as List).cast<int>(),
      monthlyCompletions: (fields[10] as List).cast<int>(),
    );
  }

  @override
  void write(BinaryWriter writer, UserStats obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.totalHabits)
      ..writeByte(1)
      ..write(obj.activeHabits)
      ..writeByte(2)
      ..write(obj.completedHabits)
      ..writeByte(3)
      ..write(obj.totalDaysTracked)
      ..writeByte(4)
      ..write(obj.currentStreakDays)
      ..writeByte(5)
      ..write(obj.longestStreakDays)
      ..writeByte(6)
      ..write(obj.averageCompletionRate)
      ..writeByte(7)
      ..write(obj.lastUpdated)
      ..writeByte(8)
      ..write(obj.categoryStats)
      ..writeByte(9)
      ..write(obj.weeklyCompletions)
      ..writeByte(10)
      ..write(obj.monthlyCompletions);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserStatsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
