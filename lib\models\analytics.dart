import 'package:hive/hive.dart';
import 'habit.dart';

part 'analytics.g.dart';

@HiveType(typeId: 6)
class Analytics extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  DateTime date;

  @HiveField(2)
  int totalHabitsCompleted;

  @HiveField(3)
  int totalHabitsScheduled;

  @HiveField(4)
  double completionRate;

  @HiveField(5)
  Map<String, int> categoryCompletions;

  @HiveField(6)
  int streakDays;

  @HiveField(7)
  List<String> completedHabitIds;

  @HiveField(8)
  DateTime createdAt;

  Analytics({
    required this.id,
    required this.date,
    required this.totalHabitsCompleted,
    required this.totalHabitsScheduled,
    required this.completionRate,
    required this.categoryCompletions,
    required this.streakDays,
    required this.completedHabitIds,
    required this.createdAt,
  });

  factory Analytics.fromHabits(List<Habit> habits, DateTime date) {
    final scheduledHabits = habits.where((h) => h.shouldDoToday && h.isActive).toList();
    final completedHabits = scheduledHabits.where((h) => h.todayCompletionPercentage == 1.0).toList();
    
    final categoryCompletions = <String, int>{};
    for (var habit in completedHabits) {
      categoryCompletions[habit.category] = (categoryCompletions[habit.category] ?? 0) + 1;
    }

    return Analytics(
      id: '${date.year}-${date.month}-${date.day}',
      date: date,
      totalHabitsCompleted: completedHabits.length,
      totalHabitsScheduled: scheduledHabits.length,
      completionRate: scheduledHabits.isEmpty ? 0.0 : completedHabits.length / scheduledHabits.length,
      categoryCompletions: categoryCompletions,
      streakDays: _calculateStreakDays(habits),
      completedHabitIds: completedHabits.map((h) => h.id).toList(),
      createdAt: DateTime.now(),
    );
  }

  static int _calculateStreakDays(List<Habit> habits) {
    if (habits.isEmpty) return 0;
    
    // Find the minimum current streak among all habits
    final activeHabits = habits.where((h) => h.isActive).toList();
    if (activeHabits.isEmpty) return 0;
    
    return activeHabits.map((h) => h.currentStreak).reduce((a, b) => a < b ? a : b);
  }
}

@HiveType(typeId: 7)
class WeeklyAnalytics extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  DateTime weekStartDate;

  @HiveField(2)
  DateTime weekEndDate;

  @HiveField(3)
  List<Analytics> dailyAnalytics;

  @HiveField(4)
  double averageCompletionRate;

  @HiveField(5)
  int totalHabitsCompleted;

  @HiveField(6)
  int totalHabitsScheduled;

  @HiveField(7)
  Map<String, int> topCategories;

  @HiveField(8)
  int bestStreakDay;

  @HiveField(9)
  DateTime createdAt;

  WeeklyAnalytics({
    required this.id,
    required this.weekStartDate,
    required this.weekEndDate,
    required this.dailyAnalytics,
    required this.averageCompletionRate,
    required this.totalHabitsCompleted,
    required this.totalHabitsScheduled,
    required this.topCategories,
    required this.bestStreakDay,
    required this.createdAt,
  });

  factory WeeklyAnalytics.fromDailyAnalytics(List<Analytics> dailyAnalytics, DateTime weekStart) {
    final weekEnd = weekStart.add(const Duration(days: 6));
    
    final totalCompleted = dailyAnalytics.fold<int>(0, (sum, a) => sum + a.totalHabitsCompleted);
    final totalScheduled = dailyAnalytics.fold<int>(0, (sum, a) => sum + a.totalHabitsScheduled);
    final avgCompletionRate = dailyAnalytics.isEmpty ? 0.0 : 
        dailyAnalytics.fold<double>(0.0, (sum, a) => sum + a.completionRate) / dailyAnalytics.length;

    // Calculate top categories
    final categoryTotals = <String, int>{};
    for (var analytics in dailyAnalytics) {
      analytics.categoryCompletions.forEach((category, count) {
        categoryTotals[category] = (categoryTotals[category] ?? 0) + count;
      });
    }

    // Find best streak day
    int bestStreak = 0;
    for (var analytics in dailyAnalytics) {
      if (analytics.streakDays > bestStreak) {
        bestStreak = analytics.streakDays;
      }
    }

    return WeeklyAnalytics(
      id: 'week-${weekStart.year}-${weekStart.month}-${weekStart.day}',
      weekStartDate: weekStart,
      weekEndDate: weekEnd,
      dailyAnalytics: dailyAnalytics,
      averageCompletionRate: avgCompletionRate,
      totalHabitsCompleted: totalCompleted,
      totalHabitsScheduled: totalScheduled,
      topCategories: categoryTotals,
      bestStreakDay: bestStreak,
      createdAt: DateTime.now(),
    );
  }
}

@HiveType(typeId: 8)
class MonthlyAnalytics extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  int month;

  @HiveField(2)
  int year;

  @HiveField(3)
  List<WeeklyAnalytics> weeklyAnalytics;

  @HiveField(4)
  double averageCompletionRate;

  @HiveField(5)
  int totalHabitsCompleted;

  @HiveField(6)
  int totalHabitsScheduled;

  @HiveField(7)
  Map<String, int> categoryBreakdown;

  @HiveField(8)
  int longestStreak;

  @HiveField(9)
  List<int> dailyCompletions; // 31 days max

  @HiveField(10)
  DateTime createdAt;

  MonthlyAnalytics({
    required this.id,
    required this.month,
    required this.year,
    required this.weeklyAnalytics,
    required this.averageCompletionRate,
    required this.totalHabitsCompleted,
    required this.totalHabitsScheduled,
    required this.categoryBreakdown,
    required this.longestStreak,
    required this.dailyCompletions,
    required this.createdAt,
  });

  factory MonthlyAnalytics.fromWeeklyAnalytics(List<WeeklyAnalytics> weeklyAnalytics, int month, int year) {
    final totalCompleted = weeklyAnalytics.fold<int>(0, (sum, w) => sum + w.totalHabitsCompleted);
    final totalScheduled = weeklyAnalytics.fold<int>(0, (sum, w) => sum + w.totalHabitsScheduled);
    final avgCompletionRate = weeklyAnalytics.isEmpty ? 0.0 : 
        weeklyAnalytics.fold<double>(0.0, (sum, w) => sum + w.averageCompletionRate) / weeklyAnalytics.length;

    // Calculate category breakdown
    final categoryTotals = <String, int>{};
    for (var weekly in weeklyAnalytics) {
      weekly.topCategories.forEach((category, count) {
        categoryTotals[category] = (categoryTotals[category] ?? 0) + count;
      });
    }

    // Calculate daily completions and longest streak
    final dailyCompletions = <int>[];
    int longestStreak = 0;
    
    for (var weekly in weeklyAnalytics) {
      for (var daily in weekly.dailyAnalytics) {
        dailyCompletions.add(daily.totalHabitsCompleted);
        if (daily.streakDays > longestStreak) {
          longestStreak = daily.streakDays;
        }
      }
    }

    return MonthlyAnalytics(
      id: 'month-$year-$month',
      month: month,
      year: year,
      weeklyAnalytics: weeklyAnalytics,
      averageCompletionRate: avgCompletionRate,
      totalHabitsCompleted: totalCompleted,
      totalHabitsScheduled: totalScheduled,
      categoryBreakdown: categoryTotals,
      longestStreak: longestStreak,
      dailyCompletions: dailyCompletions,
      createdAt: DateTime.now(),
    );
  }
}
