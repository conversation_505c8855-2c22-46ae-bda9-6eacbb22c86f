import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/database_service.dart';
import '../constants/app_constants.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final DatabaseService _databaseService = DatabaseService();
  User? _currentUser;

  User? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser != null;

  Future<void> initialize() async {
    await _loadCurrentUser();
  }

  Future<AuthResult> signUp({
    required String name,
    required String email,
    String? password,
  }) async {
    try {
      // Check if user already exists
      final existingUser = await _databaseService.getUser();
      if (existingUser != null) {
        return AuthResult.failure('User already exists. Please sign in instead.');
      }

      // Create new user
      final user = User.create(name: name, email: email);
      
      // Save user to database
      await _databaseService.saveUser(user);
      
      // Save authentication state
      await _saveAuthState(user);
      
      _currentUser = user;
      
      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('Failed to create account: ${e.toString()}');
    }
  }

  Future<AuthResult> signIn({
    required String email,
    String? password,
  }) async {
    try {
      // Get user from database
      final user = await _databaseService.getUser();
      
      if (user == null) {
        return AuthResult.failure('No account found. Please sign up first.');
      }
      
      if (user.email != email) {
        return AuthResult.failure('Invalid email address.');
      }
      
      // Save authentication state
      await _saveAuthState(user);
      
      _currentUser = user;
      
      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('Failed to sign in: ${e.toString()}');
    }
  }

  Future<void> signOut() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.keyIsAuthenticated);
      await prefs.remove(AppConstants.keyUserEmail);
      await prefs.remove(AppConstants.keyUserName);
      
      _currentUser = null;
    } catch (e) {
      throw Exception('Failed to sign out: ${e.toString()}');
    }
  }

  Future<AuthResult> updateProfile({
    String? name,
    String? email,
    UserPreferences? preferences,
  }) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('No user signed in.');
      }

      // Update user data
      final updatedUser = User(
        id: _currentUser!.id,
        name: name ?? _currentUser!.name,
        email: email ?? _currentUser!.email,
        createdAt: _currentUser!.createdAt,
        preferences: preferences ?? _currentUser!.preferences,
        stats: _currentUser!.stats,
      );

      // Save to database
      await _databaseService.saveUser(updatedUser);
      
      // Update authentication state if email changed
      if (email != null && email != _currentUser!.email) {
        await _saveAuthState(updatedUser);
      }
      
      _currentUser = updatedUser;
      
      return AuthResult.success(updatedUser);
    } catch (e) {
      return AuthResult.failure('Failed to update profile: ${e.toString()}');
    }
  }

  Future<void> deleteAccount() async {
    try {
      if (_currentUser == null) {
        throw Exception('No user signed in.');
      }

      // Delete all user data
      await _databaseService.clearAllData();
      
      // Clear authentication state
      await signOut();
      
    } catch (e) {
      throw Exception('Failed to delete account: ${e.toString()}');
    }
  }

  Future<bool> checkAuthenticationStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isAuthenticated = prefs.getBool(AppConstants.keyIsAuthenticated) ?? false;
      
      if (isAuthenticated) {
        await _loadCurrentUser();
        return _currentUser != null;
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> _loadCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isAuthenticated = prefs.getBool(AppConstants.keyIsAuthenticated) ?? false;
      
      if (isAuthenticated) {
        final user = await _databaseService.getUser();
        _currentUser = user;
      }
    } catch (e) {
      _currentUser = null;
    }
  }

  Future<void> _saveAuthState(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.keyIsAuthenticated, true);
    await prefs.setString(AppConstants.keyUserEmail, user.email);
    await prefs.setString(AppConstants.keyUserName, user.name);
  }

  // Guest mode functionality
  Future<void> continueAsGuest() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppConstants.keyIsGuest, true);
      
      // Create a temporary guest user
      _currentUser = User.create(
        name: 'Guest User',
        email: '<EMAIL>',
      );
    } catch (e) {
      throw Exception('Failed to continue as guest: ${e.toString()}');
    }
  }

  bool get isGuest {
    return _currentUser?.email == '<EMAIL>';
  }

  Future<void> convertGuestToUser({
    required String name,
    required String email,
  }) async {
    try {
      if (!isGuest) {
        throw Exception('Current user is not a guest.');
      }

      // Update guest user to real user
      final updatedUser = User.create(name: name, email: email);
      updatedUser.stats = _currentUser!.stats;
      updatedUser.preferences = _currentUser!.preferences;
      
      // Save to database
      await _databaseService.saveUser(updatedUser);
      
      // Update authentication state
      await _saveAuthState(updatedUser);
      
      // Clear guest flag
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.keyIsGuest);
      
      _currentUser = updatedUser;
    } catch (e) {
      throw Exception('Failed to convert guest account: ${e.toString()}');
    }
  }
}

class AuthResult {
  final bool isSuccess;
  final String? error;
  final User? user;

  AuthResult._({
    required this.isSuccess,
    this.error,
    this.user,
  });

  factory AuthResult.success(User user) {
    return AuthResult._(
      isSuccess: true,
      user: user,
    );
  }

  factory AuthResult.failure(String error) {
    return AuthResult._(
      isSuccess: false,
      error: error,
    );
  }
}
