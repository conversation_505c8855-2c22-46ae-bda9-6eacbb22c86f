import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../widgets/common_widgets.dart';
import '../services/auth_service.dart';
import 'main_screen.dart';

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _isSignUp = false;
  bool _isLoading = false;
  String? _error;

  final AuthService _authService = AuthService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.largeSpacing),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: AppConstants.extraLargeSpacing),
                _buildHeader(),
                const SizedBox(height: AppConstants.extraLargeSpacing),
                _buildForm(),
                const SizedBox(height: AppConstants.largeSpacing),
                _buildActionButtons(),
                const SizedBox(height: AppConstants.mediumSpacing),
                _buildToggleMode(),
                const SizedBox(height: AppConstants.largeSpacing),
                _buildGuestOption(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Icon(
          Icons.track_changes,
          size: 80,
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(height: AppConstants.mediumSpacing),
        Text(
          'Habit Tracker',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallSpacing),
        Text(
          _isSignUp 
              ? 'Create your account to start building better habits'
              : 'Welcome back! Sign in to continue your journey',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildForm() {
    return Column(
      children: [
        if (_isSignUp) ...[
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Full Name',
              prefixIcon: Icon(Icons.person),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your name';
              }
              return null;
            },
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
        ],
        
        TextFormField(
          controller: _emailController,
          decoration: const InputDecoration(
            labelText: 'Email',
            prefixIcon: Icon(Icons.email),
          ),
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your email';
            }
            if (!value.contains('@')) {
              return 'Please enter a valid email';
            }
            return null;
          },
        ),
        
        const SizedBox(height: AppConstants.mediumSpacing),
        
        TextFormField(
          controller: _passwordController,
          decoration: const InputDecoration(
            labelText: 'Password (Optional)',
            prefixIcon: Icon(Icons.lock),
            helperText: 'Leave empty for simple email-only authentication',
          ),
          obscureText: true,
        ),
        
        if (_error != null) ...[
          const SizedBox(height: AppConstants.mediumSpacing),
          Container(
            padding: const EdgeInsets.all(AppConstants.mediumSpacing),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.error, color: Colors.red),
                const SizedBox(width: AppConstants.smallSpacing),
                Expanded(
                  child: Text(
                    _error!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        CustomButton(
          text: _isSignUp ? 'Create Account' : 'Sign In',
          onPressed: _isLoading ? null : _handleAuth,
          isLoading: _isLoading,
          icon: _isSignUp ? Icons.person_add : Icons.login,
        ),
      ],
    );
  }

  Widget _buildToggleMode() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          _isSignUp 
              ? 'Already have an account? '
              : 'Don\'t have an account? ',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        TextButton(
          onPressed: () {
            setState(() {
              _isSignUp = !_isSignUp;
              _error = null;
            });
          },
          child: Text(_isSignUp ? 'Sign In' : 'Sign Up'),
        ),
      ],
    );
  }

  Widget _buildGuestOption() {
    return Column(
      children: [
        Row(
          children: [
            const Expanded(child: Divider()),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.mediumSpacing),
              child: Text(
                'OR',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ),
            const Expanded(child: Divider()),
          ],
        ),
        const SizedBox(height: AppConstants.mediumSpacing),
        CustomButton(
          text: 'Continue as Guest',
          onPressed: _isLoading ? null : _handleGuestMode,
          isOutlined: true,
          icon: Icons.person_outline,
        ),
        const SizedBox(height: AppConstants.smallSpacing),
        Text(
          'You can create an account later to sync your data',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Future<void> _handleAuth() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      AuthResult result;
      
      if (_isSignUp) {
        result = await _authService.signUp(
          name: _nameController.text.trim(),
          email: _emailController.text.trim(),
          password: _passwordController.text.isNotEmpty 
              ? _passwordController.text 
              : null,
        );
      } else {
        result = await _authService.signIn(
          email: _emailController.text.trim(),
          password: _passwordController.text.isNotEmpty 
              ? _passwordController.text 
              : null,
        );
      }

      if (result.isSuccess) {
        _navigateToMain();
      } else {
        setState(() {
          _error = result.error;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'An unexpected error occurred: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _handleGuestMode() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      await _authService.continueAsGuest();
      _navigateToMain();
    } catch (e) {
      setState(() {
        _error = 'Failed to continue as guest: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _navigateToMain() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const MainScreen(),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
