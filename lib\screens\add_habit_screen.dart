import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../constants/app_theme.dart';
import '../widgets/common_widgets.dart';
import '../models/habit.dart';

class AddHabitScreen extends StatefulWidget {
  final Habit? habitToEdit;

  const AddHabitScreen({super.key, this.habitToEdit});

  @override
  State<AddHabitScreen> createState() => _AddHabitScreenState();
}

class _AddHabitScreenState extends State<AddHabitScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String _selectedCategory = AppConstants.habitCategories.first;
  HabitFrequency _selectedFrequency = HabitFrequency.daily;
  List<String> _selectedDays = [];
  IconData _selectedIcon = AppConstants.habitIcons.first;
  Color _selectedColor = AppConstants.habitColors.first;
  bool _reminderEnabled = false;
  TimeOfDay _reminderTime = const TimeOfDay(hour: 9, minute: 0);

  @override
  void initState() {
    super.initState();
    if (widget.habitToEdit != null) {
      _loadHabitData();
    }
  }

  void _loadHabitData() {
    final habit = widget.habitToEdit!;
    _nameController.text = habit.name;
    _descriptionController.text = habit.description;
    _selectedCategory = habit.category;
    _selectedFrequency = habit.frequency;
    _selectedDays = List.from(habit.selectedDays);
    _selectedIcon = IconData(habit.icon.codePoint, fontFamily: habit.icon.fontFamily);
    _selectedColor = Color(habit.color);
    _reminderEnabled = habit.reminderEnabled;
    if (habit.reminderTime != null) {
      final parts = habit.reminderTime!.split(':');
      _reminderTime = TimeOfDay(
        hour: int.parse(parts[0]),
        minute: int.parse(parts[1]),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: widget.habitToEdit != null ? 'Edit Habit' : 'Add New Habit',
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.mediumSpacing),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: AppConstants.largeSpacing),
              _buildCategorySection(),
              const SizedBox(height: AppConstants.largeSpacing),
              _buildFrequencySection(),
              const SizedBox(height: AppConstants.largeSpacing),
              _buildCustomizationSection(),
              const SizedBox(height: AppConstants.largeSpacing),
              _buildReminderSection(),
              const SizedBox(height: AppConstants.extraLargeSpacing),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Habit Name',
              hintText: 'e.g., Morning Exercise',
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a habit name';
              }
              return null;
            },
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description (Optional)',
              hintText: 'Describe your habit...',
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Category',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          Wrap(
            spacing: AppConstants.smallSpacing,
            runSpacing: AppConstants.smallSpacing,
            children: AppConstants.habitCategories.map((category) {
              final isSelected = category == _selectedCategory;
              return FilterChip(
                label: Text(category),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _selectedCategory = category;
                  });
                },
                backgroundColor: Colors.grey[200],
                selectedColor: AppTheme.primaryColor.withOpacity(0.2),
                checkmarkColor: AppTheme.primaryColor,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFrequencySection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Frequency',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          
          // Frequency options
          Column(
            children: HabitFrequency.values.map((frequency) {
              return RadioListTile<HabitFrequency>(
                title: Text(_getFrequencyDisplayName(frequency)),
                subtitle: Text(_getFrequencyDescription(frequency)),
                value: frequency,
                groupValue: _selectedFrequency,
                onChanged: (value) {
                  setState(() {
                    _selectedFrequency = value!;
                    if (value != HabitFrequency.weekly) {
                      _selectedDays.clear();
                    }
                  });
                },
              );
            }).toList(),
          ),
          
          // Weekly days selection
          if (_selectedFrequency == HabitFrequency.weekly) ...[
            const SizedBox(height: AppConstants.mediumSpacing),
            Text(
              'Select Days',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppConstants.smallSpacing),
            Wrap(
              spacing: AppConstants.smallSpacing,
              children: AppConstants.daysOfWeek.map((day) {
                final isSelected = _selectedDays.contains(day);
                return FilterChip(
                  label: Text(AppConstants.daysOfWeekShort[AppConstants.daysOfWeek.indexOf(day)]),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedDays.add(day);
                      } else {
                        _selectedDays.remove(day);
                      }
                    });
                  },
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCustomizationSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Customization',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          
          // Icon selection
          Text(
            'Icon',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          SizedBox(
            height: 60,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: AppConstants.habitIcons.length,
              itemBuilder: (context, index) {
                final icon = AppConstants.habitIcons[index];
                final isSelected = icon == _selectedIcon;
                return Padding(
                  padding: const EdgeInsets.only(right: AppConstants.smallSpacing),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedIcon = icon;
                      });
                    },
                    child: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: isSelected ? AppTheme.primaryColor : Colors.grey[200],
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: isSelected ? AppTheme.primaryColor : Colors.grey[300]!,
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        icon,
                        color: isSelected ? Colors.white : Colors.grey[600],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          const SizedBox(height: AppConstants.mediumSpacing),
          
          // Color selection
          Text(
            'Color',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: AppConstants.habitColors.length,
              itemBuilder: (context, index) {
                final color = AppConstants.habitColors[index];
                final isSelected = color == _selectedColor;
                return Padding(
                  padding: const EdgeInsets.only(right: AppConstants.smallSpacing),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedColor = color;
                      });
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: isSelected ? Colors.black : Colors.transparent,
                          width: 3,
                        ),
                      ),
                      child: isSelected
                          ? const Icon(Icons.check, color: Colors.white)
                          : null,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReminderSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reminder',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          SwitchListTile(
            title: const Text('Enable Reminder'),
            subtitle: const Text('Get notified to complete your habit'),
            value: _reminderEnabled,
            onChanged: (value) {
              setState(() {
                _reminderEnabled = value;
              });
            },
          ),
          if (_reminderEnabled) ...[
            const SizedBox(height: AppConstants.smallSpacing),
            ListTile(
              title: const Text('Reminder Time'),
              subtitle: Text(_reminderTime.format(context)),
              trailing: const Icon(Icons.access_time),
              onTap: _selectReminderTime,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'Cancel',
            onPressed: () => Navigator.of(context).pop(),
            isOutlined: true,
          ),
        ),
        const SizedBox(width: AppConstants.mediumSpacing),
        Expanded(
          child: CustomButton(
            text: widget.habitToEdit != null ? 'Update Habit' : 'Create Habit',
            onPressed: _saveHabit,
            icon: widget.habitToEdit != null ? Icons.update : Icons.add,
          ),
        ),
      ],
    );
  }

  String _getFrequencyDisplayName(HabitFrequency frequency) {
    switch (frequency) {
      case HabitFrequency.daily:
        return 'Daily';
      case HabitFrequency.weekly:
        return 'Weekly';
      case HabitFrequency.monthly:
        return 'Monthly';
    }
  }

  String _getFrequencyDescription(HabitFrequency frequency) {
    switch (frequency) {
      case HabitFrequency.daily:
        return 'Every day';
      case HabitFrequency.weekly:
        return 'Specific days of the week';
      case HabitFrequency.monthly:
        return 'Once a month';
    }
  }

  void _selectReminderTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _reminderTime,
    );
    if (picked != null) {
      setState(() {
        _reminderTime = picked;
      });
    }
  }

  void _saveHabit() {
    if (_formKey.currentState!.validate()) {
      if (_selectedFrequency == HabitFrequency.weekly && _selectedDays.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select at least one day for weekly habits'),
          ),
        );
        return;
      }

      // Create or update habit
      final habit = Habit(
        id: widget.habitToEdit?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text,
        description: _descriptionController.text,
        category: _selectedCategory,
        color: _selectedColor.value,
        icon: _selectedIcon.codePoint.toString(),
        frequency: _selectedFrequency,
        selectedDays: _selectedDays,
        createdAt: widget.habitToEdit?.createdAt ?? DateTime.now(),
        progress: widget.habitToEdit?.progress ?? [],
        reminderEnabled: _reminderEnabled,
        reminderTime: _reminderEnabled ? '${_reminderTime.hour}:${_reminderTime.minute.toString().padLeft(2, '0')}' : null,
      );

      // TODO: Save habit to database
      
      Navigator.of(context).pop(habit);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}
