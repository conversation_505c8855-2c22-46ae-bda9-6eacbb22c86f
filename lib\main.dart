import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'constants/app_theme.dart';
import 'providers/habit_provider.dart';
import 'services/database_service.dart';
import 'services/auth_service.dart';
import 'services/notification_service.dart';
import 'screens/splash_screen.dart';
import 'models/habit.dart';
import 'models/user.dart';
import 'models/analytics.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(HabitAdapter());
  Hive.registerAdapter(HabitFrequencyAdapter());
  Hive.registerAdapter(HabitProgressAdapter());
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(UserPreferencesAdapter());
  Hive.registerAdapter(UserStatsAdapter());
  Hive.registerAdapter(AnalyticsAdapter());
  Hive.registerAdapter(DailyAnalyticsAdapter());

  // Initialize services
  await DatabaseService().initialize();
  await AuthService().initialize();
  await NotificationService().initialize();

  runApp(const HabitTrackerApp());
}

class HabitTrackerApp extends StatelessWidget {
  const HabitTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => HabitProvider()),
      ],
      child: MaterialApp(
        title: 'Habit Tracker',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const SplashScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}


