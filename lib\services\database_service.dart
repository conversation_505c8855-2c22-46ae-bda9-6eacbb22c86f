import 'package:hive_flutter/hive_flutter.dart';
import '../models/habit.dart';
import '../models/user.dart';
import '../models/analytics.dart';
import '../constants/app_constants.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  Box<Habit>? _habitBox;
  Box<User>? _userBox;
  Box<DailyAnalytics>? _analyticsBox;

  Future<void> initialize() async {
    await Hive.initFlutter();
    
    // Register adapters
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(HabitAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(HabitProgressAdapter());
    }
    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(HabitFrequencyAdapter());
    }
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(UserAdapter());
    }
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(UserPreferencesAdapter());
    }
    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(UserStatsAdapter());
    }
    if (!Hive.isAdapterRegistered(6)) {
      Hive.registerAdapter(DailyAnalyticsAdapter());
    }
    if (!Hive.isAdapterRegistered(7)) {
      Hive.registerAdapter(WeeklyAnalyticsAdapter());
    }
    if (!Hive.isAdapterRegistered(8)) {
      Hive.registerAdapter(MonthlyAnalyticsAdapter());
    }

    // Open boxes
    _habitBox = await Hive.openBox<Habit>(AppConstants.habitBoxName);
    _userBox = await Hive.openBox<User>(AppConstants.userBoxName);
    _analyticsBox = await Hive.openBox<DailyAnalytics>(AppConstants.analyticsBoxName);
  }

  // Habit operations
  Future<List<Habit>> getAllHabits() async {
    await _ensureInitialized();
    return _habitBox!.values.toList();
  }

  Future<Habit?> getHabit(String id) async {
    await _ensureInitialized();
    return _habitBox!.get(id);
  }

  Future<void> saveHabit(Habit habit) async {
    await _ensureInitialized();
    await _habitBox!.put(habit.id, habit);
  }

  Future<void> deleteHabit(String id) async {
    await _ensureInitialized();
    await _habitBox!.delete(id);
  }

  Future<List<Habit>> getHabitsByCategory(String category) async {
    await _ensureInitialized();
    return _habitBox!.values.where((habit) => habit.category == category).toList();
  }

  Future<List<Habit>> getActiveHabits() async {
    await _ensureInitialized();
    return _habitBox!.values.where((habit) => !habit.isArchived).toList();
  }

  Future<List<Habit>> getArchivedHabits() async {
    await _ensureInitialized();
    return _habitBox!.values.where((habit) => habit.isArchived).toList();
  }

  // User operations
  Future<User?> getUser() async {
    await _ensureInitialized();
    final users = _userBox!.values.toList();
    return users.isNotEmpty ? users.first : null;
  }

  Future<void> saveUser(User user) async {
    await _ensureInitialized();
    await _userBox!.put('current_user', user);
  }

  Future<void> deleteUser() async {
    await _ensureInitialized();
    await _userBox!.clear();
  }

  // Analytics operations
  Future<DailyAnalytics?> getDailyAnalytics(DateTime date) async {
    await _ensureInitialized();
    final key = _formatDateKey(date);
    return _analyticsBox!.get(key);
  }

  Future<void> saveDailyAnalytics(DailyAnalytics analytics) async {
    await _ensureInitialized();
    final key = _formatDateKey(analytics.date);
    await _analyticsBox!.put(key, analytics);
  }

  Future<List<DailyAnalytics>> getAnalyticsRange(DateTime start, DateTime end) async {
    await _ensureInitialized();
    final analytics = <DailyAnalytics>[];
    
    for (DateTime date = start; date.isBefore(end) || date.isAtSameMomentAs(end); date = date.add(const Duration(days: 1))) {
      final dailyAnalytics = await getDailyAnalytics(date);
      if (dailyAnalytics != null) {
        analytics.add(dailyAnalytics);
      }
    }
    
    return analytics;
  }

  Future<List<DailyAnalytics>> getLastNDaysAnalytics(int days) async {
    final end = DateTime.now();
    final start = end.subtract(Duration(days: days - 1));
    return getAnalyticsRange(start, end);
  }

  // Utility methods
  Future<void> updateDailyAnalytics(DateTime date) async {
    final habits = await getAllHabits();
    final activeHabits = habits.where((h) => !h.isArchived).toList();
    
    int totalHabits = 0;
    int completedHabits = 0;
    
    for (final habit in activeHabits) {
      if (habit.shouldDoToday) {
        totalHabits++;
        if (habit.progress.any((p) => 
          p.date.year == date.year &&
          p.date.month == date.month &&
          p.date.day == date.day &&
          p.isCompleted
        )) {
          completedHabits++;
        }
      }
    }
    
    final analytics = DailyAnalytics(
      date: date,
      totalHabits: totalHabits,
      completedHabits: completedHabits,
      completionRate: totalHabits > 0 ? completedHabits / totalHabits : 0.0,
      streakCount: _calculateStreakForDate(date, habits),
    );
    
    await saveDailyAnalytics(analytics);
  }

  int _calculateStreakForDate(DateTime date, List<Habit> habits) {
    int streak = 0;
    DateTime currentDate = date;
    
    while (true) {
      final habitsForDay = habits.where((h) => h.shouldDoToday).toList();
      if (habitsForDay.isEmpty) {
        currentDate = currentDate.subtract(const Duration(days: 1));
        continue;
      }
      
      final completedForDay = habitsForDay.where((h) => 
        h.progress.any((p) => 
          p.date.year == currentDate.year &&
          p.date.month == currentDate.month &&
          p.date.day == currentDate.day &&
          p.isCompleted
        )
      ).length;
      
      if (completedForDay == habitsForDay.length) {
        streak++;
        currentDate = currentDate.subtract(const Duration(days: 1));
      } else {
        break;
      }
      
      // Prevent infinite loop
      if (streak > 365) break;
    }
    
    return streak;
  }

  String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Future<void> _ensureInitialized() async {
    if (_habitBox == null || _userBox == null || _analyticsBox == null) {
      await initialize();
    }
  }

  // Backup and restore
  Future<Map<String, dynamic>> exportData() async {
    await _ensureInitialized();
    
    final habits = await getAllHabits();
    final user = await getUser();
    final analytics = _analyticsBox!.values.toList();
    
    return {
      'habits': habits.map((h) => h.toJson()).toList(),
      'user': user?.toJson(),
      'analytics': analytics.map((a) => a.toJson()).toList(),
      'exportDate': DateTime.now().toIso8601String(),
      'version': '1.0.0',
    };
  }

  Future<void> importData(Map<String, dynamic> data) async {
    await _ensureInitialized();
    
    // Clear existing data
    await _habitBox!.clear();
    await _userBox!.clear();
    await _analyticsBox!.clear();
    
    // Import habits
    if (data['habits'] != null) {
      for (final habitData in data['habits']) {
        final habit = Habit.fromJson(habitData);
        await saveHabit(habit);
      }
    }
    
    // Import user
    if (data['user'] != null) {
      final user = User.fromJson(data['user']);
      await saveUser(user);
    }
    
    // Import analytics
    if (data['analytics'] != null) {
      for (final analyticsData in data['analytics']) {
        final analytics = DailyAnalytics.fromJson(analyticsData);
        await saveDailyAnalytics(analytics);
      }
    }
  }

  Future<void> clearAllData() async {
    await _ensureInitialized();
    await _habitBox!.clear();
    await _userBox!.clear();
    await _analyticsBox!.clear();
  }

  Future<void> close() async {
    await _habitBox?.close();
    await _userBox?.close();
    await _analyticsBox?.close();
  }
}
