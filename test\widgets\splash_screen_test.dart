import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:habbit_tracker/screens/splash_screen.dart';

void main() {
  group('SplashScreen Widget Tests', () {
    testWidgets('should display app logo and name', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: SplashScreen(),
        ),
      );

      // Verify that the app icon is displayed
      expect(find.byIcon(Icons.track_changes), findsOneWidget);

      // Verify that the app name is displayed
      expect(find.text('Habit Tracker'), findsOneWidget);

      // Verify that the tagline is displayed
      expect(find.text('Build Better Habits'), findsOneWidget);
    });

    testWidgets('should show loading indicator', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: SplashScreen(),
        ),
      );

      // Verify that a loading indicator is present
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should have proper layout structure', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: SplashScreen(),
        ),
      );

      // Verify that the main structure exists
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(Column), findsAtLeastNWidgets(1));
      
      // Verify that elements are properly spaced
      expect(find.byType(SizedBox), findsAtLeastNWidgets(1));
    });

    testWidgets('should use correct theme colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            primarySwatch: Colors.blue,
          ),
          home: const SplashScreen(),
        ),
      );

      await tester.pump();

      // Find the icon widget
      final iconFinder = find.byIcon(Icons.track_changes);
      expect(iconFinder, findsOneWidget);

      // Get the Icon widget
      final Icon iconWidget = tester.widget(iconFinder);
      
      // Verify the icon uses the primary color
      expect(iconWidget.color, isNotNull);
    });

    testWidgets('should have proper text styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: SplashScreen(),
        ),
      );

      // Find the main title
      final titleFinder = find.text('Habit Tracker');
      expect(titleFinder, findsOneWidget);

      // Get the Text widget
      final Text titleWidget = tester.widget(titleFinder);
      
      // Verify text styling
      expect(titleWidget.style, isNotNull);
      expect(titleWidget.style!.fontWeight, FontWeight.bold);
    });

    testWidgets('should be responsive to different screen sizes', (WidgetTester tester) async {
      // Test with different screen sizes
      await tester.binding.setSurfaceSize(const Size(400, 800));
      
      await tester.pumpWidget(
        const MaterialApp(
          home: SplashScreen(),
        ),
      );

      expect(find.byType(SplashScreen), findsOneWidget);

      // Test with smaller screen
      await tester.binding.setSurfaceSize(const Size(300, 600));
      await tester.pump();

      expect(find.byType(SplashScreen), findsOneWidget);
      expect(find.text('Habit Tracker'), findsOneWidget);

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });
  });
}
