import 'package:flutter/foundation.dart';
import '../models/habit.dart';
import '../services/database_service.dart';
import '../services/notification_service.dart';

class HabitProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final NotificationService _notificationService = NotificationService();
  
  List<Habit> _habits = [];
  bool _isLoading = false;
  String? _error;

  List<Habit> get habits => _habits;
  bool get isLoading => _isLoading;
  String? get error => _error;

  List<Habit> get todayHabits {
    final today = DateTime.now();
    return _habits.where((habit) => habit.shouldDoToday).toList();
  }

  List<Habit> get completedTodayHabits {
    return todayHabits.where((habit) => habit.isCompletedToday).toList();
  }

  double get todayCompletionRate {
    if (todayHabits.isEmpty) return 0.0;
    return completedTodayHabits.length / todayHabits.length;
  }

  int get currentStreak {
    if (_habits.isEmpty) return 0;
    
    // Calculate the overall streak based on daily completion
    int streak = 0;
    final today = DateTime.now();
    
    for (int i = 0; i >= -365; i--) {
      final date = today.add(Duration(days: i));
      final habitsForDay = _habits.where((h) => h.shouldDoToday).toList();
      
      if (habitsForDay.isEmpty) continue;
      
      final completedForDay = habitsForDay.where((h) => 
        h.progress.any((p) => 
          p.date.year == date.year &&
          p.date.month == date.month &&
          p.date.day == date.day &&
          p.isCompleted
        )
      ).length;
      
      if (completedForDay == habitsForDay.length) {
        streak++;
      } else if (i < 0) {
        break;
      }
    }
    
    return streak;
  }

  Future<void> loadHabits() async {
    _setLoading(true);
    try {
      _habits = await _databaseService.getAllHabits();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> addHabit(Habit habit) async {
    try {
      await _databaseService.saveHabit(habit);
      _habits.add(habit);
      
      // Schedule notification if enabled
      if (habit.reminderEnabled && habit.reminderTime != null) {
        await _notificationService.scheduleHabitReminder(habit);
      }
      
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> updateHabit(Habit habit) async {
    try {
      await _databaseService.saveHabit(habit);
      
      final index = _habits.indexWhere((h) => h.id == habit.id);
      if (index != -1) {
        _habits[index] = habit;
      }
      
      // Update notification
      await _notificationService.cancelHabitReminder(habit.id);
      if (habit.reminderEnabled && habit.reminderTime != null) {
        await _notificationService.scheduleHabitReminder(habit);
      }
      
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> deleteHabit(String habitId) async {
    try {
      await _databaseService.deleteHabit(habitId);
      await _notificationService.cancelHabitReminder(habitId);
      
      _habits.removeWhere((h) => h.id == habitId);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> markHabitComplete(String habitId, {DateTime? date}) async {
    try {
      final habit = _habits.firstWhere((h) => h.id == habitId);
      final targetDate = date ?? DateTime.now();
      
      // Check if already completed for this date
      final existingProgressIndex = habit.progress.indexWhere((p) =>
        p.date.year == targetDate.year &&
        p.date.month == targetDate.month &&
        p.date.day == targetDate.day
      );
      
      if (existingProgressIndex != -1) {
        // Update existing progress
        habit.progress[existingProgressIndex] = HabitProgress(
          date: targetDate,
          isCompleted: true,
          completedAt: DateTime.now(),
        );
      } else {
        // Add new progress entry
        habit.progress.add(HabitProgress(
          date: targetDate,
          isCompleted: true,
          completedAt: DateTime.now(),
        ));
      }
      
      // Update streak
      habit.updateStreak();
      
      // Save to database
      await _databaseService.saveHabit(habit);
      
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> markHabitIncomplete(String habitId, {DateTime? date}) async {
    try {
      final habit = _habits.firstWhere((h) => h.id == habitId);
      final targetDate = date ?? DateTime.now();
      
      // Find and update progress entry
      final existingProgressIndex = habit.progress.indexWhere((p) =>
        p.date.year == targetDate.year &&
        p.date.month == targetDate.month &&
        p.date.day == targetDate.day
      );
      
      if (existingProgressIndex != -1) {
        habit.progress[existingProgressIndex] = HabitProgress(
          date: targetDate,
          isCompleted: false,
          completedAt: null,
        );
        
        // Update streak
        habit.updateStreak();
        
        // Save to database
        await _databaseService.saveHabit(habit);
        
        notifyListeners();
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  List<Habit> getHabitsByCategory(String category) {
    if (category == 'All') return _habits;
    return _habits.where((h) => h.category == category).toList();
  }

  List<Habit> searchHabits(String query) {
    if (query.isEmpty) return _habits;
    return _habits.where((h) => 
      h.name.toLowerCase().contains(query.toLowerCase()) ||
      h.description.toLowerCase().contains(query.toLowerCase()) ||
      h.category.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  Map<String, int> getCategoryStats() {
    final stats = <String, int>{};
    for (final habit in _habits) {
      stats[habit.category] = (stats[habit.category] ?? 0) + 1;
    }
    return stats;
  }

  Map<String, double> getCategoryCompletionRates() {
    final rates = <String, double>{};
    final categoryGroups = <String, List<Habit>>{};
    
    // Group habits by category
    for (final habit in _habits) {
      categoryGroups[habit.category] = (categoryGroups[habit.category] ?? [])..add(habit);
    }
    
    // Calculate completion rate for each category
    for (final entry in categoryGroups.entries) {
      final habits = entry.value;
      final totalRate = habits.fold<double>(0, (sum, habit) => sum + habit.completionRate);
      rates[entry.key] = habits.isNotEmpty ? totalRate / habits.length : 0.0;
    }
    
    return rates;
  }

  List<Map<String, dynamic>> getWeeklyProgress() {
    final weeklyData = <Map<String, dynamic>>[];
    final today = DateTime.now();
    
    for (int i = 6; i >= 0; i--) {
      final date = today.subtract(Duration(days: i));
      final habitsForDay = _habits.where((h) => h.shouldDoToday).toList();
      final completedForDay = habitsForDay.where((h) => 
        h.progress.any((p) => 
          p.date.year == date.year &&
          p.date.month == date.month &&
          p.date.day == date.day &&
          p.isCompleted
        )
      ).length;
      
      weeklyData.add({
        'date': date,
        'total': habitsForDay.length,
        'completed': completedForDay,
        'rate': habitsForDay.isNotEmpty ? completedForDay / habitsForDay.length : 0.0,
      });
    }
    
    return weeklyData;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
